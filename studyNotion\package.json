{"name": "react-tailwind-css-starter-pack", "version": "0.1.0", "private": true, "dependencies": {"@ramonak/react-progress-bar": "^5.0.3", "@reduxjs/toolkit": "^1.9.5", "axios": "^1.4.0", "chart.js": "^4.3.0", "concurrently": "^8.0.1", "copy-to-clipboard": "^3.3.3", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.44.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.8.0", "react-markdown": "^8.0.7", "react-otp-input": "^3.0.2", "react-rating-stars-component": "^2.2.0", "react-redux": "^8.0.5", "react-router-dom": "^6.11.2", "react-scripts": "5.0.1", "react-super-responsive-table": "^5.2.1", "react-type-animation": "^3.0.1", "redux-toolkit": "^1.1.2", "swiper": "^9.4.1", "video-react": "^0.16.0", "web-vitals": "^2.1.4"}, "repository": {"type": "git", "url": "git+https://github.com/thepranaygupta/react-tailwind-css-starter-pack.git"}, "author": "<PERSON><PERSON><PERSON>", "bugs": {"url": "https://github.com/thepranaygupta/react-tailwind-css-starter-pack/issues"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "server": "cd server && npm run dev", "dev": "concurrently -n \"client,server\" -c \"bgBlue,bgYellow\" \"npm start\" \"npm run server\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.2.7"}}