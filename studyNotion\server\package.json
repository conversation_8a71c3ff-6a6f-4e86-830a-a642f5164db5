{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.36.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-random-string": "^5.0.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-fileupload": "^1.4.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "node-schedule": "^2.1.1", "nodemailer": "^6.9.1", "nodemon": "^2.0.22", "otp-generator": "^4.0.1", "razorpay": "^2.8.6"}}